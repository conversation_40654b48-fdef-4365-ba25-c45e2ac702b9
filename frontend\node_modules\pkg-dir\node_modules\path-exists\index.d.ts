/**
Check if a path exists.

@returns Whether the path exists.

@example
```
// foo.ts
import {pathExists} from 'path-exists';

console.log(await pathExists('foo.ts'));
//=> true
```
*/
export function pathExists(path: string): Promise<boolean>;

/**
Synchronously check if a path exists.

@returns Whether the path exists.

@example
```
// foo.ts
import {pathExistsSync} from 'path-exists';

console.log(pathExistsSync('foo.ts'));
//=> true
```
*/
export function pathExistsSync(path: string): boolean;
