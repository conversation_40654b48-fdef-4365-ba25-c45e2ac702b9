{"version":3,"sources":["webpack:///./packages/mdc-layout-grid/mdc-layout-grid.scss","webpack:///mdc-layout-grid.scss","webpack:///./packages/mdc-layout-grid/_mixins.scss"],"names":[],"mappings":";;;;;;;AA4BA;EAEI;EAIA;EAIA;EARA;EAIA;EAIA;EARA;EAIA;EAIA;AC5BJ;;AC0DI;EFtBJ;IE2DE;IACA;IACA,aF1Da;IE2Db;ED1FA;AACF;AC4CI;EFjBJ;IE2DE;IACA;IACA,aF1Da;IE2Db;EDlFA;AACF;AC+BI;EFZJ;IE2DE;IACA;IACA,aF1Da;IE2Db;ED1EA;AACF;;ACiCI;EFZJ;IEgEE;IACA;IACA;IACA;IACA;EDhFA;ECkFA;IFtEF;MEuEI;MACA;MACA,cFrEW;MEsEX;MACA;ID/EA;EACF;AACF;ACSI;EFPJ;IEgEE;IACA;IACA;IACA;IACA;ED9DA;ECgEA;IFtEF;MEuEI;MACA;MACA,cFrEW;MEsEX;MACA;ID7DA;EACF;AACF;ACdI;EFFJ;IEgEE;IACA;IACA;IACA;IACA;ED5CA;EC8CA;IFtEF;MEuEI;MACA;MACA,cFrEW;MEsEX;MACA;ID3CA;EACF;AACF;;ACtBI;EFDJ;IEuBE;IACA;IAsDA;IACA;IACA;EDhDA;ECNA;IF1BF;ME2BI;MACA;IDSA;EACF;EC4CA;IFlFF;MEmFI;IDzCA;EACF;ED/Be;;IEWf;IACA;EDwBA;ECtBA;IFde;;MEeb;MACA;ID0BA;EACF;;ED3Ce;;IEWf;IACA;EDqCA;ECnCA;IFde;;MEeb;MACA;IDuCA;EACF;;EDxDe;;IEWf;IACA;EDkDA;EChDA;IFde;;MEeb;MACA;IDoDA;EACF;;EDrEe;;IEWf;IACA;ED+DA;EC7DA;IFde;;MEeb;MACA;IDiEA;EACF;;EDlFe;;IEWf;IACA;ED4EA;EC1EA;IFde;;MEeb;MACA;ID8EA;EACF;;ED/Fe;;IEWf;IACA;EDyFA;ECvFA;IFde;;MEeb;MACA;ID2FA;EACF;;ED5Ge;;IEWf;IACA;EDsGA;ECpGA;IFde;;MEeb;MACA;IDwGA;EACF;;EDzHe;;IEWf;IACA;EDmHA;ECjHA;IFde;;MEeb;MACA;IDqHA;EACF;;EDtIe;;IEWf;IACA;EDgIA;EC9HA;IFde;;MEeb;MACA;IDkIA;EACF;;EDnJe;;IEWf;IACA;ED6IA;EC3IA;IFde;;MEeb;MACA;ID+IA;EACF;;EDhKe;;IEWf;IACA;ED0JA;ECxJA;IFde;;MEeb;MACA;ID4JA;EACF;;ED7Ke;;IEWf;IACA;EDuKA;ECrKA;IFde;;MEeb;MACA;IDyKA;EACF;AACF;AC3MI;EFIJ;IEuBE;IACA;IAsDA;IACA;IACA;ED+HA;ECrLA;IF1BF;ME2BI;MACA;IDwLA;EACF;ECnIA;IFlFF;MEmFI;IDsIA;EACF;ED9Me;;IEWf;IACA;EDuMA;ECrMA;IFde;;MEeb;MACA;IDyMA;EACF;;ED1Ne;;IEWf;IACA;EDoNA;EClNA;IFde;;MEeb;MACA;IDsNA;EACF;;EDvOe;;IEWf;IACA;EDiOA;EC/NA;IFde;;MEeb;MACA;IDmOA;EACF;;EDpPe;;IEWf;IACA;ED8OA;EC5OA;IFde;;MEeb;MACA;IDgPA;EACF;;EDjQe;;IEWf;IACA;ED2PA;ECzPA;IFde;;MEeb;MACA;ID6PA;EACF;;ED9Qe;;IEWf;IACA;EDwQA;ECtQA;IFde;;MEeb;MACA;ID0QA;EACF;;ED3Re;;IEWf;IACA;EDqRA;ECnRA;IFde;;MEeb;MACA;IDuRA;EACF;;EDxSe;;IEWf;IACA;EDkSA;EChSA;IFde;;MEeb;MACA;IDoSA;EACF;;EDrTe;;IEWf;IACA;ED+SA;EC7SA;IFde;;MEeb;MACA;IDiTA;EACF;;EDlUe;;IEWf;IACA;ED4TA;EC1TA;IFde;;MEeb;MACA;ID8TA;EACF;;ED/Ue;;IEWf;IACA;EDyUA;ECvUA;IFde;;MEeb;MACA;ID2UA;EACF;;ED5Ve;;IEWf;IACA;EDsVA;ECpVA;IFde;;MEeb;MACA;IDwVA;EACF;AACF;AC/XI;EFSJ;IEuBE;IACA;IAsDA;IACA;IACA;ED8SA;ECpWA;IF1BF;ME2BI;MACA;IDuWA;EACF;EClTA;IFlFF;MEmFI;IDqTA;EACF;ED7Xe;;IEWf;IACA;EDsXA;ECpXA;IFde;;MEeb;MACA;IDwXA;EACF;;EDzYe;;IEWf;IACA;EDmYA;ECjYA;IFde;;MEeb;MACA;IDqYA;EACF;;EDtZe;;IEWf;IACA;EDgZA;EC9YA;IFde;;MEeb;MACA;IDkZA;EACF;;EDnae;;IEWf;IACA;ED6ZA;EC3ZA;IFde;;MEeb;MACA;ID+ZA;EACF;;EDhbe;;IEWf;IACA;ED0aA;ECxaA;IFde;;MEeb;MACA;ID4aA;EACF;;ED7be;;IEWf;IACA;EDubA;ECrbA;IFde;;MEeb;MACA;IDybA;EACF;;ED1ce;;IEWf;IACA;EDocA;EClcA;IFde;;MEeb;MACA;IDscA;EACF;;EDvde;;IEWf;IACA;EDidA;EC/cA;IFde;;MEeb;MACA;IDmdA;EACF;;EDpee;;IEWf;IACA;ED8dA;EC5dA;IFde;;MEeb;MACA;IDgeA;EACF;;EDjfe;;IEWf;IACA;ED2eA;ECzeA;IFde;;MEeb;MACA;ID6eA;EACF;;ED9fe;;IEWf;IACA;EDwfA;ECtfA;IFde;;MEeb;MACA;ID0fA;EACF;;ED3gBe;;IEWf;IACA;EDqgBA;ECngBA;IFde;;MEeb;MACA;IDugBA;EACF;AACF;AD/gBI;EEkEF,QFnEa;ACmhBf;ADlhBI;EEkEF,QFnEa;ACshBf;ADrhBI;EEkEF,QFnEa;ACyhBf;ADxhBI;EEkEF,QFnEa;AC4hBf;AD3hBI;EEkEF,QFnEa;AC+hBf;AD9hBI;EEkEF,QFnEa;ACkiBf;ADjiBI;EEkEF,QFnEa;ACqiBf;ADpiBI;EEkEF,QFnEa;ACwiBf;ADviBI;EEkEF,QFnEa;AC2iBf;AD1iBI;EEkEF,SFnEa;AC8iBf;AD7iBI;EEkEF,SFnEa;ACijBf;ADhjBI;EEkEF,SFnEa;ACojBf;AD7iBE;EEiEE;AD+eJ;AC7eI;EFnEF;IEoEI;EDgfJ;AACF;ADjjBE;EEqEE;AD+eJ;ADhjBE;EEqEE;AD8eJ;AC5eI;EFvEF;IEwEI;ED+eJ;AACF;;AC3lBI;EFwCJ;IEqFE;IAEA;;;;GAAA;EDseA;AACF;AC3mBI;EF6CJ;IEqFE;IAEA;;;;GAAA;EDgfA;AACF;AC1nBI;EFkDJ;IEqFE;IAEA;;;;GAAA;ED0fA;AACF;;ADjkBA;EACE;EACA;ACokBF;;ADjkBA;EACE;EACA;ACokBF,C","file":"mdc.layout-grid.css","sourcesContent":["// Copyright 2017 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use 'sass:list';\n@use 'sass:map';\n@use './variables';\n@use './mixins';\n\n:root {\n  @each $size in map.keys(variables.$columns) {\n    --mdc-layout-grid-margin-#{$size}: #{map.get(\n        variables.$default-margin,\n        $size\n      )};\n    --mdc-layout-grid-gutter-#{$size}: #{map.get(\n        variables.$default-gutter,\n        $size\n      )};\n    --mdc-layout-grid-column-width-#{$size}: #{map.get(\n        variables.$column-width,\n        $size\n      )};\n  }\n}\n\n// postcss-bem-linter: define layout-grid\n.mdc-layout-grid {\n  @each $size in map.keys(variables.$columns) {\n    @include mixins.media-query_($size) {\n      $margin: map.get(variables.$default-margin, $size);\n\n      @include mixins.layout-grid($size, $margin, variables.$max-width);\n    }\n  }\n}\n\n.mdc-layout-grid__inner {\n  @each $size in map.keys(variables.$columns) {\n    @include mixins.media-query_($size) {\n      $margin: map.get(variables.$default-margin, $size);\n      $gutter: map.get(variables.$default-gutter, $size);\n\n      @include mixins.inner($size, $margin, $gutter);\n    }\n  }\n}\n\n.mdc-layout-grid__cell {\n  // select the upper breakpoint\n  $upper-breakpoint: list.nth(map.keys(variables.$columns), 1);\n\n  @each $size in map.keys(variables.$columns) {\n    @include mixins.media-query_($size) {\n      $gutter: map.get(variables.$default-gutter, $size);\n\n      @include mixins.cell($size, variables.$default-column-span, $gutter);\n\n      @for $span from 1 through map.get(variables.$columns, $upper-breakpoint) {\n        // Span classes.\n        @at-root .mdc-layout-grid__cell--span-#{$span},\n          .mdc-layout-grid__cell--span-#{$span}-#{$size} {\n          @include mixins.cell-span_($size, $span, $gutter);\n        }\n      }\n    }\n  }\n\n  // Order override classes.\n  @for $i from 1 through map.get(variables.$columns, $upper-breakpoint) {\n    &--order-#{$i} {\n      @include mixins.cell-order($i);\n    }\n  }\n\n  // Alignment classes.\n  &--align-top {\n    @include mixins.cell-align(top);\n  }\n\n  &--align-middle {\n    @include mixins.cell-align(middle);\n  }\n\n  &--align-bottom {\n    @include mixins.cell-align(bottom);\n  }\n}\n\n.mdc-layout-grid--fixed-column-width {\n  @each $size in map.keys(variables.$columns) {\n    @include mixins.media-query_($size) {\n      $margin: map.get(variables.$default-margin, $size);\n      $gutter: map.get(variables.$default-gutter, $size);\n      $column-width: map.get(variables.$column-width, $size);\n\n      @include mixins.fixed-column-width(\n        $size,\n        $margin,\n        $gutter,\n        $column-width\n      );\n    }\n  }\n}\n\n.mdc-layout-grid--align-left {\n  margin-right: auto;\n  margin-left: 0;\n}\n\n.mdc-layout-grid--align-right {\n  margin-right: 0;\n  margin-left: auto;\n}\n// postcss-bem-linter: end\n",":root {\n  --mdc-layout-grid-margin-desktop: 24px;\n  --mdc-layout-grid-gutter-desktop: 24px;\n  --mdc-layout-grid-column-width-desktop: 72px;\n  --mdc-layout-grid-margin-tablet: 16px;\n  --mdc-layout-grid-gutter-tablet: 16px;\n  --mdc-layout-grid-column-width-tablet: 72px;\n  --mdc-layout-grid-margin-phone: 16px;\n  --mdc-layout-grid-gutter-phone: 16px;\n  --mdc-layout-grid-column-width-phone: 72px;\n}\n\n@media (min-width: 840px) {\n  .mdc-layout-grid {\n    box-sizing: border-box;\n    margin: 0 auto;\n    padding: 24px;\n    padding: var(--mdc-layout-grid-margin-desktop, 24px);\n  }\n}\n@media (min-width: 600px) and (max-width: 839px) {\n  .mdc-layout-grid {\n    box-sizing: border-box;\n    margin: 0 auto;\n    padding: 16px;\n    padding: var(--mdc-layout-grid-margin-tablet, 16px);\n  }\n}\n@media (max-width: 599px) {\n  .mdc-layout-grid {\n    box-sizing: border-box;\n    margin: 0 auto;\n    padding: 16px;\n    padding: var(--mdc-layout-grid-margin-phone, 16px);\n  }\n}\n\n@media (min-width: 840px) {\n  .mdc-layout-grid__inner {\n    display: flex;\n    flex-flow: row wrap;\n    align-items: stretch;\n    margin: -12px;\n    margin: calc(var(--mdc-layout-grid-gutter-desktop, 24px) / 2 * -1);\n  }\n  @supports (display: grid) {\n    .mdc-layout-grid__inner {\n      display: grid;\n      margin: 0;\n      grid-gap: 24px;\n      grid-ga