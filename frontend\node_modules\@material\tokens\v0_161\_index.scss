//
// !!! THIS FILE WAS AUTOMATICALLY GENERATED !!!
// !!! DO NOT MODIFY IT BY HAND !!!
//

@forward './md-comp-assist-chip' as md-comp-assist-chip-*;
@forward './md-comp-badge' as md-comp-badge-*;
@forward './md-comp-banner' as md-comp-banner-*;
@forward './md-comp-bottom-app-bar' as md-comp-bottom-app-bar-*;
@forward './md-comp-carousel-item' as md-comp-carousel-item-*;
@forward './md-comp-checkbox' as md-comp-checkbox-*;
@forward './md-comp-circular-progress-indicator' as
  md-comp-circular-progress-indicator-*;
@forward './md-comp-data-table' as md-comp-data-table-*;
@forward './md-comp-date-input-modal' as md-comp-date-input-modal-*;
@forward './md-comp-date-picker-docked' as md-comp-date-picker-docked-*;
@forward './md-comp-date-picker-modal' as md-comp-date-picker-modal-*;
@forward './md-comp-dialog' as md-comp-dialog-*;
@forward './md-comp-divider' as md-comp-divider-*;
@forward './md-comp-elevated-button' as md-comp-elevated-button-*;
@forward './md-comp-elevated-card' as md-comp-elevated-card-*;
@forward './md-comp-extended-fab-branded' as md-comp-extended-fab-branded-*;
@forward './md-comp-extended-fab-primary' as md-comp-extended-fab-primary-*;
@forward './md-comp-extended-fab-secondary' as md-comp-extended-fab-secondary-*;
@forward './md-comp-extended-fab-surface' as md-comp-extended-fab-surface-*;
@forward './md-comp-extended-fab-tertiary' as md-comp-extended-fab-tertiary-*;
@forward './md-comp-fab-branded' as md-comp-fab-branded-*;
@forward './md-comp-fab-branded-large' as md-comp-fab-branded-large-*;
@forward './md-comp-fab-primary' as md-comp-fab-primary-*;
@forward './md-comp-fab-primary-large' as md-comp-fab-primary-large-*;
@forward './md-comp-fab-primary-small' as md-comp-fab-primary-small-*;
@forward './md-comp-fab-secondary' as md-comp-fab-secondary-*;
@forward './md-comp-fab-secondary-large' as md-comp-fab-secondary-large-*;
@forward './md-comp-fab-secondary-small' as md-comp-fab-secondary-small-*;
@forward './md-comp-fab-surface' as md-comp-fab-surface-*;
@forward './md-comp-fab-surface-large' as md-comp-fab-surface-large-*;
@forward './md-comp-fab-surface-small' as md-comp-fab-surface-small-*;
@forward './md-comp-fab-tertiary' as md-comp-fab-tertiary-*;
@forward './md-comp-fab-tertiary-large' as md-comp-fab-tertiary-large-*;
@forward './md-comp-fab-tertiary-small' as md-comp-fab-tertiary-small-*;
@forward './md-comp-filled-autocomplete' as md-comp-filled-autocomplete-*;
@forward './md-comp-filled-button' as md-comp-filled-button-*;
@forward './md-comp-filled-card' as md-comp-filled-card-*;
@forward './md-comp-filled-icon-button' as md-comp-filled-icon-button-*;
@forward './md-comp-filled-menu-button' as md-comp-filled-menu-button-*;
@forward './md-comp-filled-select' as md-comp-filled-select-*;
@forward './md-comp-filled-text-field' as md-comp-filled-text-field-*;
@forward './md-comp-filled-tonal-button' as md-comp-filled-tonal-button-*;
@forward './md-comp-filled-tonal-icon-button' as
  md-comp-filled-tonal-icon-button-*;
@forward './md-comp-filter-chip' as md-comp-filter-chip-*;
@forward './md-comp-full-screen-dialog' as md-comp-full-screen-dialog-*;
@forward './md-comp-icon-button' as md-comp-icon-button-*;
@forward './md-comp-input-chip' as md-comp-input-chip-*;
@forward './md-comp-linear-progress-indicator' as
  md-comp-linear-progress-indicator-*;
@forward './md-comp-list' as md-comp-list-*;
@forward './md-comp-menu' as md-comp-menu-*;
@forward './md-comp-navigation-bar' as md-comp-navigation-bar-*;
@forward './md-comp-navigation-drawer' as md-comp-navigation-drawer-*;
@forward './md-comp-navigation-rail' as md-comp-navigation-rail-*;
@forward './md-comp-outlined-autocomplete' as md-comp-outlined-autocomplete-*;
@forward './md-comp-outlined-button' as md-comp-outlined-button-*;
@forward './md-comp-outlined-card' as md-comp-outlined-card-*;
@forward './md-comp-outlined-icon-button' as md-comp-outlined-icon-button-*;
@forward './md-comp-outlined-menu-button' as md-comp-outlined-menu-button-*;
@forward './md-comp-outlined-segmented-button' as
  md-comp-outlined-segmented-button-*;
@forward './md-comp-outlined-select' as md-comp-outlined-select-*;
@forward './md-comp-outlined-text-field' as md-comp-outlined-text-field-*;
@forward './md-comp-plain-tooltip' as md-comp-plain-tooltip-*;
@forward './md-comp-primary-navigation-tab' as md-comp-primary-navigation-tab-*;
@forward './md-comp-radio-button' as md-comp-radio-button-*;
@forward './md-comp-rich-tooltip' as md-comp-rich-tooltip-*;
@forward './md-comp-scrim' as md-comp-scrim-*;
@forward './md-comp-search-bar' as md-comp-search-bar-*;
@forward './md-comp-search-view' as md-comp-search-view-*;
@forward './md-comp-secondary-navigation-tab' as
  md-comp-secondary-navigation-tab-*;
@forward './md-comp-sheet-bottom' as md-comp-sheet-bottom-*;
@forward './md-comp-sheet-floating' as md-comp-sheet-floating-*;
@forward './md-comp-sheet-side' as md-comp-sheet-side-*;
@forward './md-comp-slider' as md-comp-slider-*;
@forward './md-comp-snackbar' as md-comp-snackbar-*;
@forward './md-comp-standard-menu-button' as md-comp-standard-menu-button-*;
@forward './md-comp-suggestion-chip' as md-comp-suggestion-chip-*;
@forward './md-comp-switch' as md-comp-switch-*;
@forward './md-comp-text-button' as md-comp-text-button-*;
@forward './md-comp-time-input' as md-comp-time-input-*;
@forward './md-comp-time-picker' as md-comp-time-picker-*;
@forward './md-comp-top-app-bar-large' as md-comp-top-app-bar-large-*;
@forward './md-comp-top-app-bar-medium' as md-comp-top-app-bar-medium-*;
@forward './md-comp-top-app-bar-small' as md-comp-top-app-bar-small-*;
@forward './md-comp-top-app-bar-small-centered' as
  md-comp-top-app-bar-small-centered-*;
@forward './md-ref-palette' as md-ref-palette-*;
@forward './md-ref-typeface' as md-ref-typeface-*;
@forward './md-sys-color' as md-sys-color-*;
@forward './md-sys-elevation' as md-sys-elevation-*;
@forward './md-sys-motion' as md-sys-motion-*;
@forward './md-sys-shape' as md-sys-shape-*;
@forward './md-sys-state' as md-sys-state-*;
@forward './md-sys-typescale' as md-sys-typescale-*;
