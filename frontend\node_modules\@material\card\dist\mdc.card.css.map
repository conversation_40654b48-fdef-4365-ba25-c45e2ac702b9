{"version":3,"sources":["webpack:///./packages/mdc-card/mdc-card.scss","webpack:///./packages/material-components-web/node_modules/@material/theme/_css.scss","webpack:///./packages/material-components-web/node_modules/@material/theme/_gss.scss","webpack:///mdc-card.scss","webpack:///./packages/mdc-card/_mixins.scss","webpack:///./packages/material-components-web/node_modules/@material/elevation/_elevation-theme.scss","webpack:///./packages/material-components-web/node_modules/@material/dom/_dom.scss","webpack:///./packages/material-components-web/node_modules/@material/rtl/_rtl.scss","webpack:///./packages/material-components-web/node_modules/@material/ripple/_ripple.scss","webpack:///./packages/material-components-web/node_modules/@material/animation/_animation.scss","webpack:///./packages/material-components-web/node_modules/@material/ripple/_ripple-theme.scss"],"names":[],"mappings":";;;;;;;AA4DE;EAGM;EACA;EACA;ECCF;ECZF;EDwBA;EDFI;ECVF;ECZF;EDwBA;AEnEJ;;ACmSE;EH5OI;ECZF;EDwBA;EAZE;ECZF;EDwBA;EIoMA;EACA;EJrMA;AEvDJ;AE+QE;EJxNE;EAAA;EI8NI;EHjPJ;EAAc;EGmPV;AF/QR;ACuRI;EHpPE;ECZF;EDwBA;AE1CJ;;ACuRE;EH7OE;EAAA;EGkTA;EHlTA;AEnCJ;;ACSE;EA4WA;EACA;EACA;ADjXF;ACSI;EElBA;EACA;EACA;EACA;EACA;EJeA;EAAc;EIbd;EACA;EACA;EACA;EACA;EFWI;ADGR;AGiCI;EFvCA;IEFE;EHYJ;AACF;;ACAI;EAEI;ADER;;ACGE;EAEI;EACA;ADDN;;ACSE;EAEI;EACA;EACA;EACA;EACA;ADPN;ACUI;EAEI;EACA;ADTR;;ACcE;EAEI;EACA;ADZN;;ACgBE;EAEI;EACA;ADdN;;AC+SE;EAGI;AD9SN;;AC2SE;EAGI;AD1SN;;ACkBE;EAEI;EACA;EACA;EACA;EACA;EACA;ADhBN;;ACwBE;EAmRA;EACA;EACA;EAjRI;EACA;EACA;EACA;EACA;EACA;ADrBN;;ACyBE;EAEI;EACA;ADvBN;;AC2BE;EAEI;EACA;ADzBN;;ACiCE;EA0PA,aAD+B;EAE/B;EACA;EACA;EAzPI;EACA;AD7BN;;ACiCE;EAEI;AD/BN;;ACmCE;;EA2OA,aAD+B;EAE/B;EACA;EACA;ADzQF;;ACkCE;EHpHE;EG0HE;EACA;ADnCN;;ACuCE;EFlJE;EAAc;EDmBd;ECnBA;EAAc;EDmBd;AEiGJ;AI/FM;EACE;ELtBJ;EAAc;EDmBd;ECnBA;EAAc;EDmBd;EMKI;AJsGR;;AC8BE;EA+MA,oBA7M6B;EA8M7B;EACA;EACA;EA9MI;EACA;EACA;KAAA;MAAA;UAAA;AD1BN;AC6BI;EAEI;AD5BR;;ACqCE;EFhLE;EAAc;EDmBd;ECnBA;EAAc;EDmBd;EGiKE;AD/BN;AIhIM;EACE;ELtBJ;EAAc;EDmBd;ECnBA;EAAc;EDmBd;EMKI;AJuIR;;ACwBI;EFvLA;EAAc;EDmBd;ECnBA;EAAc;EDmBd;AEqJJ;AInJM;EACE;ELtBJ;EAAc;EDmBd;ECnBA;EAAc;EDmBd;EMKI;AJ0JR;;ACYE;EAEI;EACA;EACA;EACA;EACA;EACA;EFrMF;EAAc;EEuMZ;ADTN;AIzKM;EACE;ELtBJ;EAAc;EE6MV;EGrLA;AJ6KR;;ACiBE;EAII;EAGA;ADnBN;;ACuBE;EH9ME;AE2LJ;;AKwCE;EACE;IACE,+DC5R2B;YD4R3B,uDC5R2B;IPoC7B;IAAc;IM8PZ;YAAA;ELxCJ;EK2CE;INjQA;IAAc;IMmQZ;YAAA;ELxCJ;AACF;;AK0BE;EACE;IACE,+DC5R2B;YD4R3B,uDC5R2B;IPoC7B;IAAc;IM8PZ;YAAA;ELxCJ;EK2CE;INjQA;IAAc;IMmQZ;YAAA;ELxCJ;AACF;AK4CE;EACE;IACE;YAAA;IACA;EL1CJ;EK6CE;IACE;EL3CJ;AACF;AKmCE;EACE;IACE;YAAA;IACA;EL1CJ;EK6CE;IACE;EL3CJ;AACF;AK8CE;EACE;IACE;YAAA;IACA;EL5CJ;EK+CE;IACE;EL7CJ;AACF;AKqCE;EACE;IACE;YAAA;IACA;EL5CJ;EK+CE;IACE;EL7CJ;AACF;ACiCE;EIxNE;EACA;EACA;EACA;EACA;EACA;EAEA;EAGE;ALuLN;AKnLE;;EAGI;EACA;EACA;EACA;EACA;ALoLN;AKhLE;EAGI;EP5EA;ECZF;EDwBA;AEmPJ;AKtKE;EPzFI;ECZF;EDwBA;AEwPJ;AK/JI;EAEI;UAAA;ALgKR;AK5JI;EAEI;ENpHJ;EAAc;EMsHV;EACA;UAAA;EACA;UAAA;AL8JR;AKxJI;EAEI;ENhIJ;EAAc;EMkIV;AL0JR;AKpJI;EAEI;UAAA;ALqJR;AK7II;EAEI;UAAA;ENpJJ;EAAc;EM0JV;UAAA;AL2IR;AK7HE;;EAGI;EN3KF;EAAc;EM6KZ;EACA;EACA;AL+HN;AK1HI;EAEI;EACA;AL2HR;AOjNI;ET1FE;ECZF;EDwBA;AEoSJ;AOqNE;ETrgBI;ECZF;EDwBA;AEySJ;AOgNE;EApRI;ETjPA;ECZF;EDwBA;AE+SJ;AOgNE;EA/PQ;APkDV;AO6ME;EAxPU,yBA7SO;ET0Bb;ECZF;EDwBA;AEwTJ;AOtCI;ETlRA;AE2TJ;ACpDI;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADqDR;AChDM;EE3TF;EACA;EACA;EACA;EACA;EJeA;EAAc;EIbd;EACA;EACA;EACA;EACA;AH+WJ;AGhUI;EFkQE;IE3SA;EH6WJ;AACF,C","file":"mdc.card.css","sourcesContent":["//\n// Copyright 2017 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use '@material/theme/custom-properties';\n@use '@material/base/mixins' as base-mixins;\n@use '@material/feature-targeting/feature-targeting';\n@use '@material/theme/theme';\n@use '@material/theme/theme-color';\n@use './elevation-theme';\n\n@mixin core-styles($query: feature-targeting.all()) {\n  $feat-animation: feature-targeting.create-target($query, animation);\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @for $z-value from 0 through 24 {\n    .mdc-elevation--z#{$z-value} {\n      @include elevation-theme.elevation($z-value, $query: $query);\n    }\n  }\n\n  .mdc-elevation-transition {\n    @include feature-targeting.targets($feat-animation) {\n      transition: elevation-theme.transition-value();\n    }\n\n    @include feature-targeting.targets($feat-structure) {\n      will-change: elevation-theme.$property;\n    }\n  }\n}\n\n///\n/// Called once per application to set up the global default elevation styles.\n///\n@mixin overlay-common($query: feature-targeting.all()) {\n  $feat-animation: feature-targeting.create-target($query, animation);\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  .mdc-elevation-overlay {\n    @include feature-targeting.targets($feat-structure) {\n      @include base-mixins.emit-once('mdc-elevation/common/structure') {\n        position: absolute;\n        border-radius: inherit;\n        pointer-events: none;\n\n        @include theme.property(\n          opacity,\n          custom-properties.create(--mdc-elevation-overlay-opacity, 0)\n        );\n      }\n    }\n\n    @include feature-targeting.targets($feat-animation) {\n      @include base-mixins.emit-once('mdc-elevation/common/animation') {\n        transition: elevation-theme.overlay-transition-value();\n      }\n    }\n\n    @include base-mixins.emit-once('mdc-elevation/common/color') {\n      $fill-color: custom-properties.create(\n        --mdc-elevation-overlay-color,\n        elevation-theme.$overlay-color\n      );\n      @include elevation-theme.overlay-fill-color($fill-color, $query: $query);\n    }\n  }\n}\n","//\n// Copyright 2020 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n@use 'sass:list';\n@use 'sass:map';\n@use 'sass:meta';\n@use './gss';\n\n/// When true, add an additional property/value declaration before declarations\n/// that use advanced features such as custom properties or CSS functions. This\n/// adds fallback support for older browsers such as IE11 that do not support\n/// these features at the cost of additional CSS. Set this variable to false to\n/// disable generating fallback declarations.\n$enable-fallback-declarations: true !default;\n\n/// Writes a CSS property/value declaration. This mixin is used throughout the\n/// theme package for consistency for dynamically setting CSS property values.\n///\n/// This mixin may optionally take a fallback value. For advanced features such\n/// as custom properties or CSS functions like min and max, a fallback value is\n/// recommended to support older browsers.\n///\n/// @param {String} $property - The CSS property of the declaration.\n/// @param {*} $value - The value of the CSS declaration. The value should be\n///     resolved by other theme functions first (i.e. custom property Maps and\n///     Material theme keys are not supported in this mixin). If the value is\n///     null, no declarations will be emitted.\n/// @param {*} $fallback - An optional fallback value for older browsers. If\n///     provided, a second property/value declaration will be added before the\n///     main property/value declaration.\n/// @param {Map} $gss - An optional Map of GSS annotations to add.\n/// @param {Bool} $important - If true, add `!important` to the declaration.\n@mixin declaration(\n  $property,\n  $value,\n  $fallback-value: null,\n  $gss: (),\n  $important: false\n) {\n  // Normally setting a null value to a property will not emit CSS, so mixins\n  // wouldn't need to check this. However, Sass will throw an error if the\n  // interpolated property is a custom property.\n  @if $value != null {\n    $important-rule: if($important, ' !important', '');\n\n    @if $fallback-value and $enable-fallback-declarations {\n      @include gss.annotate($gss);\n      #{$property}: #{$fallback-value} #{$important-rule};\n\n      // Add @alternate to annotations.\n      $gss: map.merge(\n        $gss,\n        (\n          alternate: true,\n        )\n      );\n    }\n\n    @include gss.annotate($gss);\n    #{$property}: #{$value}#{$important-rule};\n  }\n}\n\n/// Unpacks shorthand values for CSS properties (i.e. lists of 1-3 values).\n/// If a list of 4 values is given, it is returned as-is.\n///\n/// Examples:\n///\n/// unpack-value(4px) => 4px 4px 4px 4px\n/// unpack-value(4px 2px) => 4px 2px 4px 2px\n/// unpack-value(4px 2px 2px) => 4px 2px 2px 2px\n/// unpack-value(4px 2px 0 2px) => 4px 2px 0 2px\n///\n/// @param {Number | Map | List} $value - List of 1 to 4 value numbers.\n/// @return {List} a List of 4 value numbers.\n@function unpack-value($value) {\n  @if meta.type-of($value) == 'map' or list.length($value) == 1 {\n    @return $value $value $value $value;\n  } @else if list.length($value) == 4 {\n    @return $value;\n  } @else if list.length($value) == 3 {\n    @return list.nth($value, 1) list.nth($value, 2) list.nth($value, 3)\n      list.nth($value, 2);\n  } @else if list.length($value) == 2 {\n    @return list.nth($value, 1) list.nth($value, 2) list.nth($value, 1)\n      list.nth($value, 2);\n  }\n\n  @error \"Invalid CSS property value: '#{$value}' is more than 4 values\";\n}\n","//\n// Copyright 2020 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n@use 'sass:list';\n@use 'sass:map';\n@use 'sass:meta';\n\n/// Adds optional GSS annotation comments. Useful for theme mixins where one or\n/// more properties are set indirectly.\n///\n/// Annotations may be provided as a Map of annotations or as named arguments.\n///\n/// @example - scss\n///   @include annotate((noflip: true));\n///   left: 0;\n///\n/// @example - scss\n///   @include annotate($noflip: true);\n///   left: 0;\n///\n/// @example - css\n///   /* @noflip */ /*rtl:ignore*/\n///   left: 0;\n///\n/// @param {Map} $annotations - Map of annotations. Values must be set to `true`\n///     for an annotation to be added.\n@mixin annotate($annotations...) {\n  $keywords: meta.keywords($annotations);\n  @if list.length($annotations) > 0 {\n    $annotations: list.nth($annotations, 1);\n  } @else {\n    $annotations: $keywords;\n  }\n\n  @if (map.get($annotations, alternate) == true) {\n    /* @alternate */\n  }\n\n  // noflip must be the last tag right before the property\n  @if (map.get($annotations, noflip) == true) {\n    /* @noflip */ /*rtl:ignore*/\n  }\n}\n",".mdc-elevation-overlay {\n  position: absolute;\n  border-radius: inherit;\n  pointer-events: none;\n  opacity: 0;\n  /* @alternate */\n  opacity: var(--mdc-elevation-overlay-opacity, 0);\n  transition: opacity 280ms cubic-bezier(0.4, 0, 0.2, 1);\n  background-color: #fff;\n  /* @alternate */\n  background-color: var(--mdc-elevation-overlay-color, #fff);\n}\n\n.mdc-card {\n  border-radius: 4px;\n  /* @alternate */\n  border-radius: var(--mdc-shape-medium, 4px);\n  background-color: #fff;\n  /* @alternate */\n  background-color: var(--mdc-theme-surface, #fff);\n  /* @alternate */\n  position: relative;\n  box-shadow: 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);\n}\n.mdc-card .mdc-elevation-overlay {\n  width: 100%;\n  height: 100%;\n  top: 0;\n  /* @noflip */\n  /*rtl:ignore*/\n  left: 0;\n}\n.mdc-card::after {\n  border-radius: 4px;\n  /* @alternate */\n  border-radius: var(--mdc-shape-medium, 4px);\n}\n\n.mdc-card--outlined {\n  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);\n  border-width: 1px;\n  border-style: solid;\n  border-color: #e0e0e0;\n}\n\n.mdc-card {\n  display: flex;\n  flex-direction: column;\n  box-sizing: border-box;\n}\n.mdc-card::after {\n  position: absolute;\n  box-sizing: border-box;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  /* @noflip */\n  /*rtl:ignore*/\n  left: 0;\n  border: 1px solid transparent;\n  border-radius: inherit;\n  content: \"\";\n  pointer-events: none;\n  pointer-events: none;\n}\n@media screen and (forced-colors: active) {\n  .mdc-card::after {\n    border-color: CanvasText;\n  }\n}\n\n.mdc-card--outlined::after {\n  border: none;\n}\n\n.mdc-card__content {\n  border-radius: inherit;\n  height: 100%;\n}\n\n.mdc-card__media {\n  position: relative;\n  box-sizing: border-box;\n  background-repeat: no-repeat;\n  background-position: center;\n  background-size: cover;\n}\n.mdc-card__media::before {\n  display: block;\n  content: \"\";\n}\n\n.mdc-card__media:first-child {\n  border-top-left-radius: inherit;\n  border-top-right-radius: inherit;\n}\n\n.mdc-card__media:last-child {\n  border-bottom-left-radius: inherit;\n  border-bottom-right-radius: inherit;\n}\n\n.mdc-card__media--square::before {\n  margin-top: 100%;\n}\n\n.mdc-card__media--16-9::before {\n  margin-top: 56.25%;\n}\n\n.mdc-card__media-content {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  box-sizing: border-box;\n}\n\n.mdc-card__primary-action {\n  display: flex;\n  flex-direction: column;\n  box-sizing: border-box;\n  position: relative;\n  outline: none;\n  color: inherit;\n  text-decoration: none;\n  cursor: pointer;\n  overflow: hidden;\n}\n\n.mdc-card__primary-action:first-child {\n  border-top-left-radius: inherit;\n  border-top-right-radius: inherit;\n}\n\n.mdc-card__primary-action:last-child {\n  border-bottom-left-radius: inherit;\n  border-bottom-right-radius: inherit;\n}\n\n.mdc-card__actions {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  box-sizing: border-box;\n  min-height: 52px;\n  padding: 8px;\n}\n\n.mdc-card__actions--full-bleed {\n  padding: 0;\n}\n\n.mdc-card__action-buttons,\n.mdc-card__action-icons {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  box-sizing: border-box;\n}\n\n.mdc-card__action-icons {\n  color: rgba(0, 0, 0, 0.6);\n  flex-grow: 1;\n  justify-content: flex-end;\n}\n\n.mdc-card__action-buttons + .mdc-card__action-icons {\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-left: 16px;\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-right: 0;\n}\n[dir=rtl] .mdc-card__action-buttons + .mdc-card__action-icons, .mdc-card__action-buttons + .mdc-card__action-icons[dir=rtl] {\n  /*rtl:begin:ignore*/\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-left: 0;\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-right: 16px;\n  /*rtl:end:ignore*/\n}\n\n.mdc-card__action {\n  display: inline-flex;\n  flex-direction: row;\n  align-items: center;\n  box-sizing: border-box;\n  justify-content: center;\n  cursor: pointer;\n  user-select: none;\n}\n.mdc-card__action:focus {\n  outline: none;\n}\n\n.mdc-card__action--button {\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-left: 0;\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-right: 8px;\n  padding: 0 8px;\n}\n[dir=rtl] .mdc-card__action--button, .mdc-card__action--button[dir=rtl] {\n  /*rtl:begin:ignore*/\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-left: 8px;\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-right: 0;\n  /*rtl:end:ignore*/\n}\n\n.mdc-card__action--button:last-child {\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-left: 0;\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-right: 0;\n}\n[dir=rtl] .mdc-card__action--button:last-child, .mdc-card__action--button:last-child[dir=rtl] {\n  /*rtl:begin:ignore*/\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-left: 0;\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-right: 0;\n  /*rtl:end:ignore*/\n}\n\n.mdc-card__actions--full-bleed .mdc-card__action--button {\n  justify-content: space-between;\n  width: 100%;\n  height: auto;\n  max-height: none;\n  margin: 0;\n  padding: 8px 16px;\n  /* @noflip */\n  /*rtl:ignore*/\n  text-align: left;\n}\n[dir=rtl] .mdc-card__actions--full-bleed .mdc-card__action--button, .mdc-card__actions--full-bleed .mdc-card__action--button[dir=rtl] {\n  /*rtl:begin:ignore*/\n  /* @noflip */\n  /*rtl:ignore*/\n  text-align: right;\n  /*rtl:end:ignore*/\n}\n\n.mdc-card__action--icon {\n  margin: -6px 0;\n  padding: 12px;\n}\n\n.mdc-card__action--icon:not(:disabled) {\n  color: rgba(0, 0, 0, 0.6);\n}\n\n@keyframes mdc-ripple-fg-radius-in {\n  from {\n    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    /* @noflip */\n    /*rtl:ignore*/\n    transform: translate(var(--mdc-ripple-fg-translate-start, 0)) scale(1);\n  }\n  to {\n    /* @noflip */\n    /*rtl:ignore*/\n    transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));\n  }\n}\n@keyframes mdc-ripple-fg-opacity-in {\n  from {\n    animation-timing-function: linear;\n    opacity: 0;\n  }\n  to {\n    opacity: var(--mdc-ripple-fg-opacity, 0);\n  }\n}\n@keyframes mdc-ripple-fg-opacity-out {\n  from {\n    animation-timing-function: linear;\n    opacity: var(--mdc-ripple-fg-opacity, 0);\n  }\n  to {\n    opacity: 0;\n  }\n}\n.mdc-card__primary-action {\n  --mdc-ripple-fg-size: 0;\n  --mdc-ripple-left: 0;\n  --mdc-ripple-top: 0;\n  --mdc-ripple-fg-scale: 1;\n  --mdc-ripple-fg-translate-end: 0;\n  --mdc-ripple-fg-translate-start: 0;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n  will-change: transform, opacity;\n}\n.mdc-card__primary-action .mdc-card__ripple::before,\n.mdc-card__primary-action .mdc-card__ripple::after {\n  position: absolute;\n  border-radius: 50%;\n  opacity: 0;\n  pointer-events: none;\n  content: \"\";\n}\n.mdc-card__primary-action .mdc-card__ripple::before {\n  transition: opacity 15ms linear, background-color 15ms linear;\n  z-index: 1;\n  /* @alternate */\n  z-index: var(--mdc-ripple-z-index, 1);\n}\n.mdc-card__primary-action .mdc-card__ripple::after {\n  z-index: 0;\n  /* @alternate */\n  z-index: var(--mdc-ripple-z-index, 0);\n}\n.mdc-card__primary-action.mdc-ripple-upgraded .mdc-card__ripple::before {\n  transform: scale(var(--mdc-ripple-fg-scale, 1));\n}\n.mdc-card__primary-actio