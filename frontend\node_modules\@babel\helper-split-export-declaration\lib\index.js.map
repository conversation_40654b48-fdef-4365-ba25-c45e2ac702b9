{"version": 3, "names": ["_t", "require", "cloneNode", "exportNamedDeclaration", "exportSpecifier", "identifier", "variableDeclaration", "variableDeclarator", "splitExportDeclaration", "exportDeclaration", "isExportDeclaration", "isExportAllDeclaration", "Error", "isExportDefaultDeclaration", "declaration", "get", "standaloneDeclaration", "isFunctionDeclaration", "isClassDeclaration", "exportExpr", "isFunctionExpression", "isClassExpression", "scope", "isScope", "parent", "id", "node", "needBindingRegistration", "generateUidIdentifier", "hasBinding", "name", "updatedDeclaration", "updatedExportDeclaration", "insertAfter", "replaceWith", "registerDeclaration", "length", "bindingIdentifiers", "getOuterBindingIdentifiers", "specifiers", "Object", "keys", "map", "aliasDeclar"], "sources": ["../src/index.ts"], "sourcesContent": ["import {\n  cloneNode,\n  exportNamedDeclaration,\n  exportSpecifier,\n  identifier,\n  variableDeclaration,\n  variableDeclarator,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n// eslint-disable-next-line import/no-extraneous-dependencies -- TODO: Avoid cycle\nimport type { NodePath } from \"@babel/traverse\";\n\nexport default function splitExportDeclaration(\n  exportDeclaration: NodePath<\n    t.ExportDefaultDeclaration | t.ExportNamedDeclaration\n  >,\n) {\n  if (\n    !exportDeclaration.isExportDeclaration() ||\n    exportDeclaration.isExportAllDeclaration()\n  ) {\n    throw new Error(\"Only default and named export declarations can be split.\");\n  }\n\n  // build specifiers that point back to this export declaration\n\n  if (exportDeclaration.isExportDefaultDeclaration()) {\n    const declaration = exportDeclaration.get(\"declaration\");\n    const standaloneDeclaration =\n      declaration.isFunctionDeclaration() || declaration.isClassDeclaration();\n    const exportExpr =\n      declaration.isFunctionExpression() || declaration.isClassExpression();\n\n    const scope = declaration.isScope()\n      ? declaration.scope.parent\n      : declaration.scope;\n\n    // @ts-expect-error id is not defined in expressions other than function/class\n    let id = declaration.node.id;\n    let needBindingRegistration = false;\n\n    if (!id) {\n      needBindingRegistration = true;\n\n      id = scope.generateUidIdentifier(\"default\");\n\n      if (standaloneDeclaration || exportExpr) {\n        declaration.node.id = cloneNode(id);\n      }\n    } else if (exportExpr && scope.hasBinding(id.name)) {\n      needBindingRegistration = true;\n\n      id = scope.generateUidIdentifier(id.name);\n    }\n\n    const updatedDeclaration = standaloneDeclaration\n      ? declaration.node\n      : variableDeclaration(\"var\", [\n          variableDeclarator(\n            cloneNode(id),\n            // @ts-expect-error When `standaloneDeclaration` is false, declaration must not be a Function/ClassDeclaration\n            declaration.node,\n          ),\n        ]);\n\n    const updatedExportDeclaration = exportNamedDeclaration(null, [\n      exportSpecifier(cloneNode(id), identifier(\"default\")),\n    ]);\n\n    exportDeclaration.insertAfter(updatedExportDeclaration);\n    exportDeclaration.replaceWith(updatedDeclaration);\n\n    if (needBindingRegistration) {\n      scope.registerDeclaration(exportDeclaration);\n    }\n\n    return exportDeclaration;\n  } else if (exportDeclaration.get(\"specifiers\").length > 0) {\n    throw new Error(\"It doesn't make sense to split exported specifiers.\");\n  }\n\n  const declaration = exportDeclaration.get(\"declaration\");\n  const bindingIdentifiers = declaration.getOuterBindingIdentifiers();\n\n  const specifiers = Object.keys(bindingIdentifiers).map(name => {\n    return exportSpecifier(identifier(name), identifier(name));\n  });\n\n  const aliasDeclar = exportNamedDeclaration(null, specifiers);\n\n  exportDeclaration.insertAfter(aliasDeclar);\n  exportDeclaration.replaceWith(declaration.node);\n  return exportDeclaration;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAOsB;EANpBC,SAAS;EACTC,sBAAsB;EACtBC,eAAe;EACfC,UAAU;EACVC,mBAAmB;EACnBC;AAAkB,IAAAP,EAAA;AAML,SAASQ,sBAAsBA,CAC5CC,iBAEC,EACD;EACA,IACE,CAACA,iBAAiB,CAACC,mBAAmB,CAAC,CAAC,IACxCD,iBAAiB,CAACE,sBAAsB,CAAC,CAAC,EAC1C;IACA,MAAM,IAAIC,KAAK,CAAC,0DAA0D,CAAC;EAC7E;EAIA,IAAIH,iBAAiB,CAACI,0BAA0B,CAAC,CAAC,EAAE;IAClD,MAAMC,WAAW,GAAGL,iBAAiB,CAACM,GAAG,CAAC,aAAa,CAAC;IACxD,MAAMC,qBAAqB,GACzBF,WAAW,CAACG,qBAAqB,CAAC,CAAC,IAAIH,WAAW,CAACI,kBAAkB,CAAC,CAAC;IACzE,MAAMC,UAAU,GACdL,WAAW,CAACM,oBAAoB,CAAC,CAAC,IAAIN,WAAW,CAACO,iBAAiB,CAAC,CAAC;IAEvE,MAAMC,KAAK,GAAGR,WAAW,CAACS,OAAO,CAAC,CAAC,GAC/BT,WAAW,CAACQ,KAAK,CAACE,MAAM,GACxBV,WAAW,CAACQ,KAAK;IAGrB,IAAIG,EAAE,GAAGX,WAAW,CAACY,IAAI,CAACD,EAAE;IAC5B,IAAIE,uBAAuB,GAAG,KAAK;IAEnC,IAAI,CAACF,EAAE,EAAE;MACPE,uBAAuB,GAAG,IAAI;MAE9BF,EAAE,GAAGH,KAAK,CAACM,qBAAqB,CAAC,SAAS,CAAC;MAE3C,IAAIZ,qBAAqB,IAAIG,UAAU,EAAE;QACvCL,WAAW,CAACY,IAAI,CAACD,EAAE,GAAGvB,SAAS,CAACuB,EAAE,CAAC;MACrC;IACF,CAAC,MAAM,IAAIN,UAAU,IAAIG,KAAK,CAACO,UAAU,CAACJ,EAAE,CAACK,IAAI,CAAC,EAAE;MAClDH,uBAAuB,GAAG,IAAI;MAE9BF,EAAE,GAAGH,KAAK,CAACM,qBAAqB,CAACH,EAAE,CAACK,IAAI,CAAC;IAC3C;IAEA,MAAMC,kBAAkB,GAAGf,qBAAqB,GAC5CF,WAAW,CAACY,IAAI,GAChBpB,mBAAmB,CAAC,KAAK,EAAE,CACzBC,kBAAkB,CAChBL,SAAS,CAACuB,EAAE,CAAC,EAEbX,WAAW,CAACY,IACd,CAAC,CACF,CAAC;IAEN,MAAMM,wBAAwB,GAAG7B,sBAAsB,CAAC,IAAI,EAAE,CAC5DC,eAAe,CAACF,SAAS,CAACuB,EAAE,CAAC,EAAEpB,UAAU,CAAC,SAAS,CAAC,CAAC,CACtD,CAAC;IAEFI,iBAAiB,CAACwB,WAAW,CAACD,wBAAwB,CAAC;IACvDvB,iBAAiB,CAACyB,WAAW,CAACH,kBAAkB,CAAC;IAEjD,IAAIJ,uBAAuB,EAAE;MAC3BL,KAAK,CAACa,mBAAmB,CAAC1B,iBAAiB,CAAC;IAC9C;IAEA,OAAOA,iBAAiB;EAC1B,CAAC,MAAM,IAAIA,iBAAiB,CAACM,GAAG,CAAC,YAAY,CAAC,CAACqB,MAAM,GAAG,CAAC,EAAE;IACzD,MAAM,IAAIxB,KAAK,CAAC,qDAAqD,CAAC;EACxE;EAEA,MAAME,WAAW,GAAGL,iBAAiB,CAACM,GAAG,CAAC,aAAa,CAAC;EACxD,MAAMsB,kBAAkB,GAAGvB,WAAW,CAACwB,0BAA0B,CAAC,CAAC;EAEnE,MAAMC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACJ,kBAAkB,CAAC,CAACK,GAAG,CAACZ,IAAI,IAAI;IAC7D,OAAO1B,eAAe,CAACC,UAAU,CAACyB,IAAI,CAAC,EAAEzB,UAAU,CAACyB,IAAI,CAAC,CAAC;EAC5D,CAAC,CAAC;EAEF,MAAMa,WAAW,GAAGxC,sBAAsB,CAAC,IAAI,EAAEoC,UAAU,CAAC;EAE5D9B,iBAAiB,CAACwB,WAAW,CAACU,WAAW,CAAC;EAC1ClC,iBAAiB,CAACyB,WAAW,CAACpB,WAAW,CAACY,IAAI,CAAC;EAC/C,OAAOjB,iBAAiB;AAC1B", "ignoreList": []}