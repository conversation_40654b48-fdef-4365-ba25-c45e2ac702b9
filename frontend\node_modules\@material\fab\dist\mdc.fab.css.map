{"version":3,"sources":["webpack:///./packages/mdc-fab/mdc-fab.scss","webpack:///mdc-fab.scss","webpack:///./packages/material-components-web/node_modules/@material/elevation/_elevation.scss","webpack:///./packages/material-components-web/node_modules/@material/theme/_css.scss","webpack:///./packages/material-components-web/node_modules/@material/theme/_gss.scss","webpack:///./packages/mdc-fab/_fab.scss","webpack:///./packages/material-components-web/node_modules/@material/elevation/_elevation-theme.scss","webpack:///./packages/mdc-fab/_fab-theme.scss","webpack:///./packages/material-components-web/node_modules/@material/ripple/_ripple-theme.scss","webpack:///./packages/material-components-web/node_modules/@material/focus-ring/_focus-ring.scss","webpack:///./packages/material-components-web/node_modules/@material/dom/_dom.scss","webpack:///./packages/material-components-web/node_modules/@material/typography/_typography.scss","webpack:///./packages/mdc-fab/_extended-fab-theme.scss","webpack:///./packages/material-components-web/node_modules/@material/rtl/_rtl.scss","webpack:///./packages/material-components-web/node_modules/@material/ripple/_ripple.scss","webpack:///./packages/material-components-web/node_modules/@material/animation/_animation.scss"],"names":[],"mappings":";;;;;;;AAwCE;EAOM;AC7CR;;AC0DE;EAGM;EACA;EACA;ECCF;ECZF;EDwBA;EDFI;ECVF;ECZF;EDwBA;AF/DJ;;AI4CE;ECuNE;EACA;ED3GA;EACA;EACA;EACA;EACA;EACA,WEnIK;EFoIL,YEpIK;EFqIL;EACA;EACA;EACA;EACA;EACA;KAAA;MAAA;UAAA;EACA;EACA;EAGA;EAOA;EAAA;EAAA;AJ3JJ;AKiQE;EHxNE;EAAA;EG8NI;EFjPJ;EAAc;EEmPV;ALjQR;AI6II;EACE;AJ3IN;AIyJE;EAEI;EACA;AJxJN;AI4JE;EFlIE;AFvBJ;AO2cE;ELpbE;AFpBJ;AI8JE;EAEI;AJ7JN;AIkKI;EIjKA;EACA;EACA;EACA;EACA;EACA;ELRA;EAAc;EKUd;ELVA;EAAc;EKYd;UAAA;EAmFA,wBAd0B;EAe1B,uBAT4B;ARrEhC;ASoBI;EL8HA;IIrJE;EROJ;AACF;AQLI;EACE;EACA;EACA,kBAtDgB;EAuDhB;EACA;EACA;ELxBF;EAAc;EK0BZ;EL1BF;EAAc;EK4BZ;UAAA;EACA,wBA9BgB;EA+BhB,uBA/BgB;ARwCtB;ASDI;EDpBA;IAeI;ERUN;AACF;AI8HE;EFvJE;AF4BJ;AIgIE;EAOI;AJpIN;AIwIE;EAEI;AJvIN;AIgJE;EAEI;AJ/IN;;AIpDE;EA6ME,WEjOU;EFkOV,YElOU;AN6Ed;;AIrDE;EMsQE;EACA;ERxQE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;UAAA;EAZE;ECZF;EDwBA;EAAA;ECnBA;EAAc;EDmBd;ECnBA;EAAc;EDmBd;EE0NA;EACA;EACA,YOzPc;EP2Pd;EACA;AJ/IJ;AWqEE;ETrJE;AFmFJ;AWPE;ER/FE;EAAc;EDmBd;ECnBA;EAAc;EDmBd;AF2FJ;AYzFM;EACE;ETtBJ;EAAc;EDmBd;ECnBA;EAAc;EDmBd;EUKI;AZgGR;;AWlBE;ERtGE;EAAc;EDmBd;ECnBA;EAAc;EDmBd;AF8GJ;AY5GM;EACE;ETtBJ;EAAc;EDmBd;ECnBA;EAAc;EDmBd;EUKI;AZmHR;;AI/HE;EL8DI,eAJwB;EAKxB,kBALwB;EAyCtB,iBAJ0B;EAK1B,gBAL0B;ACuClC;AI/HI;ELTA;EACA;EACA,YAtCK;EI0BL;EAAc;EJkBZ;EACA,WA7CG;EI0BL;EAAc;EJqBZ;UAAA;ACyIN;;AInIE;EK9CE;EACA;EACA;EACA;EACA;ENeA;EAAc;EMbd;EACA;EACA;EACA;EACA;ATsLJ;ASvII;ELXF;IK9BI;EToLJ;AACF;;AInJE;EA6OE;EACA;EACA;EACA;EACA;AJtFJ;;AIvJE;EAwNE;EAAA;EAAA;EAQA;EACA;AJpEJ;;AIvJE;EA+OE;EACA;EACA;AJpFJ;;AIzJE;EAsPE;UAAA;EACA;EAIA;EAAA;EAAA;AJ5FJ;AIgGE;EAEI;UAAA;EAIA;EAAA;EAAA;AJlGN;;AI1JE;EF/CI;ECZF;EDwBA;EAAA;AFoMJ;AM9BE;EJtKE;EAAA;EAAA;AFyMJ;AMtBI;EJ/LE;ECZF;EDwBA;AF8MJ;AMsUE;EJphBE;AFiNJ;AMyUI;EJ1hBA;AFoNJ;;AaeE;EACE;IACE,+DC5R2B;YD4R3B,uDC5R2B;IXoC7B;IAAc;IU8PZ;YAAA;EbfJ;EakBE;IVjQA;IAAc;IUmQZ;YAAA;EbfJ;AACF;;AaCE;EACE;IACE,+DC5R2B;YD4R3B,uDC5R2B;IXoC7B;IAAc;IU8PZ;YAAA;EbfJ;EakBE;IVjQA;IAAc;IUmQZ;YAAA;EbfJ;AACF;AamBE;EACE;IACE;YAAA;IACA;EbjBJ;EaoBE;IACE;EblBJ;AACF;AaUE;EACE;IACE;YAAA;IACA;EbjBJ;EaoBE;IACE;EblBJ;AACF;AaqBE;EACE;IACE;YAAA;IACA;EbnBJ;EasBE;IACE;EbpBJ;AACF;AaYE;EACE;IACE;YAAA;IACA;EbnBJ;EasBE;IACE;EbpBJ;AACF;AInME;ESbE;EACA;EACA;EACA;EACA;EACA;EAEA;EAGE;AbgNN;Aa5ME;;EAGI;EACA;EACA;EACA;EACA;Ab6MN;AazME;EAGI;EX5EA;ECZF;EDwBA;AF4QJ;Aa/LE;EXzFI;ECZF;EDwBA;AFiRJ;AaxLI;EAEI;UAAA;AbyLR;AarLI;EAEI;EVpHJ;EAAc;EUsHV;EACA;UAAA;EACA;UAAA;AbuLR;AajLI;EAEI;EVhIJ;EAAc;EUkIV;AbmLR;Aa7KI;EAEI;UAAA;Ab8KR;AatKI;EAEI;UAAA;EVpJJ;EAAc;EU0JV;UAAA;AboKR;AatJE;;EAGI;EV3KF;EAAc;EU6KZ;EACA;EACA;AbwJN;AanJI;EAEI;EACA;AboJR;AO1OI;EL1FE;ECZF;EDwBA;AF6TJ;AO4LE;ELrgBI;ECZF;EDwBA;AFkUJ;AOuLE;EApRI;ELjPA;ECZF;EDwBA;AFwUJ;AOuLE;EA/PQ;AP2EV;AOoLE;EAxPU,yBA7SO;EL0Bb;ECZF;EDwBA;AFiVJ;AO/DI;ELlRA;AFoVJ;AInRI;ESsJA;EACA;EACA;EACA;EACA;EAGA;ETzJI;AJwRR;AOoHE;EAEI;APnHN;AOuHE;;ELneI;ECZF;EDwBA;AFsWJ,C","file":"mdc.fab.css","sourcesContent":["//\n// Copyright 2019 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use 'sass:math';\n@use '@material/base/mixins' as base-mixins;\n@use '@material/feature-targeting/feature-targeting';\n@use '@material/rtl/rtl';\n@use '@material/theme/theme';\n@use '@material/theme/keys';\n@use '@material/theme/custom-properties';\n\n$height: 48px !default;\n$width: $height !default;\n\n/// Styles applied to the component's touch target wrapper element.\n@mixin wrapper($query: feature-targeting.all()) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  .mdc-touch-target-wrapper {\n    @include feature-targeting.targets($feat-structure) {\n      // Ensure that styles are only emitted once across all components that\n      // have increased touch targets.\n      @include base-mixins.emit-once('mdc-touch-target/wrapper') {\n        // NOTE: Will change to `inline-block` in the future, but keeping as is\n        // temporarily for backwards-compatibility.\n        display: inline;\n      }\n    }\n  }\n}\n\n/// Styles applied to the component's inner touch target element.\n/// By default, only sets the inner element height to the minimum touch target\n/// height ($mdc-touch-target-height).\n/// @param {Boolean} $set-width [false] - Sets the inner element width to the\n///     minimum touch target width ($mdc-touch-target-width).\n/// @param $height [$mdc-touch-target-height] - Touch target height.\n/// @param $width [$mdc-touch-target-width] - Touch target width.\n@mixin touch-target(\n  $set-width: false,\n  $query: feature-targeting.all(),\n  $height: $height,\n  $width: $width\n) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include feature-targeting.targets($feat-structure) {\n    position: absolute;\n    top: 50%;\n    height: $height;\n  }\n\n  @if $set-width {\n    @include feature-targeting.targets($feat-structure) {\n      @include rtl.ignore-next-line();\n      left: 50%;\n      width: $width;\n      @include rtl.ignore-next-line();\n      transform: translate(-50%, -50%);\n    }\n  } @else {\n    @include feature-targeting.targets($feat-structure) {\n      left: 0;\n      right: 0;\n      transform: translateY(-50%);\n    }\n  }\n}\n\n/// Applies margin to the component with the increased touch target,\n/// to compensate for the touch target.\n@mixin margin(\n  $component-height,\n  $component-width: null,\n  $touch-target-height: $height,\n  $touch-target-width: $width,\n  $query: feature-targeting.all()\n) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include feature-targeting.targets($feat-structure) {\n    @if keys.is-key($touch-target-height) or\n      keys.is-key($component-height) or\n      custom-properties.is-custom-prop($touch-target-height) or\n      custom-properties.is-custom-prop($component-height) or\n      custom-properties.is-custom-prop-string($touch-target-height) or\n      custom-properties.is-custom-prop-string($component-height)\n    {\n      // Custom properties\n      @include theme.property(\n        margin-top,\n        'max((touch-target-height - component-height) / 2, 0px)',\n        $replace: (\n          component-height: $component-height,\n          touch-target-height: $touch-target-height\n        )\n      );\n      @include theme.property(\n        margin-bottom,\n        'max((touch-target-height - component-height) / 2, 0px)',\n        $replace: (\n          component-height: $component-height,\n          touch-target-height: $touch-target-height\n        )\n      );\n    } @else {\n      // Static values\n      $vertical-margin-value: math.div(\n        $touch-target-height - $component-height,\n        2\n      );\n      margin-top: $vertical-margin-value;\n      margin-bottom: $vertical-margin-value;\n    }\n  }\n\n  @if $component-width {\n    @include feature-targeting.targets($feat-structure) {\n      @if keys.is-key($touch-target-width) or\n        keys.is-key($component-width) or\n        custom-properties.is-custom-prop($touch-target-width) or\n        custom-properties.is-custom-prop($component-width) or\n        custom-properties.is-custom-prop-string($touch-target-width) or\n        custom-properties.is-custom-prop-string($component-width)\n      {\n        // Custom properties\n        @include theme.property(\n          margin-right,\n          'max((touch-target-width - component-width) / 2, 0px)',\n          $replace: (\n            component-width: $component-width,\n            touch-target-width: $touch-target-width\n          )\n        );\n        @include theme.property(\n          margin-left,\n          'max((touch-target-width - component-width) / 2), 0px',\n          $replace: (\n            component-width: $component-width,\n            touch-target-width: $touch-target-width\n          )\n        );\n      } @else {\n        // Static values\n        $horizontal-margin-value: math.div(\n          $touch-target-width - $component-width,\n          2\n        );\n        margin-right: $horizontal-margin-value;\n        margin-left: $horizontal-margin-value;\n      }\n    }\n  }\n}\n",".mdc-touch-target-wrapper {\n  display: inline;\n}\n\n.mdc-elevation-overlay {\n  position: absolute;\n  border-radius: inherit;\n  pointer-events: none;\n  opacity: 0;\n  /* @alternate */\n  opacity: var(--mdc-elevation-overlay-opacity, 0);\n  transition: opacity 280ms cubic-bezier(0.4, 0, 0.2, 1);\n  background-color: #fff;\n  /* @alternate */\n  background-color: var(--mdc-elevation-overlay-color, #fff);\n}\n\n.mdc-fab {\n  /* @alternate */\n  position: relative;\n  display: inline-flex;\n  position: relative;\n  align-items: center;\n  justify-content: center;\n  box-sizing: border-box;\n  width: 56px;\n  height: 56px;\n  padding: 0;\n  border: none;\n  fill: currentColor;\n  text-decoration: none;\n  cursor: pointer;\n  user-select: none;\n  -moz-appearance: none;\n  -webkit-appearance: none;\n  overflow: visible;\n  transition: box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1), opacity 15ms linear 30ms, transform 270ms 0ms cubic-bezier(0, 0, 0.2, 1);\n}\n.mdc-fab .mdc-elevation-overlay {\n  width: 100%;\n  height: 100%;\n  top: 0;\n  /* @noflip */\n  /*rtl:ignore*/\n  left: 0;\n}\n.mdc-fab[hidden] {\n  display: none;\n}\n.mdc-fab::-moz-focus-inner {\n  padding: 0;\n  border: 0;\n}\n.mdc-fab:hover {\n  box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);\n}\n.mdc-fab.mdc-ripple-upgraded--background-focused, .mdc-fab:not(.mdc-ripple-upgraded):focus {\n  box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);\n}\n.mdc-fab .mdc-fab__focus-ring {\n  position: absolute;\n}\n.mdc-fab.mdc-ripple-upgraded--background-focused .mdc-fab__focus-ring, .mdc-fab:not(.mdc-ripple-upgraded):focus .mdc-fab__focus-ring {\n  pointer-events: none;\n  border: 2px solid transparent;\n  border-radius: 6px;\n  box-sizing: content-box;\n  position: absolute;\n  top: 50%;\n  /* @noflip */\n  /*rtl:ignore*/\n  left: 50%;\n  /* @noflip */\n  /*rtl:ignore*/\n  transform: translate(-50%, -50%);\n  height: calc(100% + 4px);\n  width: calc(100% + 4px);\n}\n@media screen and (forced-colors: active) {\n  .mdc-fab.mdc-ripple-upgraded--background-focused .mdc-fab__focus-ring, .mdc-fab:not(.mdc-ripple-upgraded):focus .mdc-fab__focus-ring {\n    border-color: CanvasText;\n  }\n}\n.mdc-fab.mdc-ripple-upgraded--background-focused .mdc-fab__focus-ring::after, .mdc-fab:not(.mdc-ripple-upgraded):focus .mdc-fab__focus-ring::after {\n  content: \"\";\n  border: 2px solid transparent;\n  border-radius: 8px;\n  display: block;\n  position: absolute;\n  top: 50%;\n  /* @noflip */\n  /*rtl:ignore*/\n  left: 50%;\n  /* @noflip */\n  /*rtl:ignore*/\n  transform: translate(-50%, -50%);\n  height: calc(100% + 4px);\n  width: calc(100% + 4px);\n}\n@media screen and (forced-colors: active) {\n  .mdc-fab.mdc-ripple-upgraded--background-focused .mdc-fab__focus-ring::after, .mdc-fab:not(.mdc-ripple-upgraded):focus .mdc-fab__focus-ring::after {\n    border-color: CanvasText;\n  }\n}\n.mdc-fab:active, .mdc-fab:focus:active {\n  box-shadow: 0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12);\n}\n.mdc-fab:active, .mdc-fab:focus {\n  outline: none;\n}\n.mdc-fab:hover {\n  cursor: pointer;\n}\n.mdc-fab > svg {\n  width: 100%;\n}\n\n.mdc-fab--mini {\n  width: 40px;\n  height: 40px;\n}\n\n.mdc-fab--extended {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n  font-family: Roboto, sans-serif;\n  /* @alternate */\n  font-family: var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));\n  font-size: 0.875rem;\n  /* @alternate */\n  font-size: var(--mdc-typography-button-font-size, 0.875rem);\n  line-height: 2.25rem;\n  /* @alternate */\n  line-height: var(--mdc-typography-button-line-height, 2.25rem);\n  font-weight: 500;\n  /* @alternate */\n  font-weight: var(--mdc-typography-button-font-weight, 500);\n  letter-spacing: 0.0892857143em;\n  /* @alternate */\n  letter-spacing: var(--mdc-typography-button-letter-spacing, 0.0892857143em);\n  text-decoration: none;\n  /* @alternate */\n  text-decoration: var(--mdc-typography-button-text-decoration, none);\n  text-transform: uppercase;\n  /* @alternate */\n  text-transform: var(--mdc-typography-button-text-transform, uppercase);\n  border-radius: 24px;\n  /* @noflip */\n  /*rtl:ignore*/\n  padding-left: 20px;\n  /* @noflip */\n  /*rtl:ignore*/\n  padding-right: 20px;\n  width: auto;\n  max-width: 100%;\n  height: 48px;\n  /* @alternate */\n  line-height: normal;\n}\n.mdc-fab--extended .mdc-fab__ripple {\n  border-radius: 24px;\n}\n.mdc-fab--extended .mdc-fab__icon {\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-left: calc(12px - 20px);\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-right: 12px;\n}\n[dir=rtl] .mdc-fab--extended .mdc-fab__icon, .mdc-fab--extended .mdc-fab__icon[dir=rtl] {\n  /*rtl:begin:ignore*/\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-left: 12px;\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-right: calc(12px - 20px);\n  /*rtl:end:ignore*/\n}\n\n.mdc-fab--extended .mdc-fab__label + .mdc-fab__icon {\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-left: 12px;\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-right: calc(12px - 20px);\n}\n[dir=rtl] .mdc-fab--extended .mdc-fab__label + .mdc-fab__icon, .mdc-fab--extended .mdc-fab__label + .mdc-fab__icon[dir=rtl] {\n  /*rtl:begin:ignore*/\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-left: calc(12px - 20px);\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-right: 12px;\n  /*rtl:end:ignore*/\n}\n\n.mdc-fab--touch {\n  margin-top: 4px;\n  margin-bottom: 4px;\n  margin-right: 4px;\n  margin-left: 4px;\n}\n.mdc-fab--touch .mdc-fab__touch {\n  position: absolute;\n  top: 50%;\n  height: 48px;\n  /* @noflip */\n  /*rtl:ignore*/\n  left: 50%;\n  width: 48px;\n  /* @noflip */\n  /*rtl:ignore*/\n  transform: translate(-50%, -50%);\n}\n\n.mdc-fab::before {\n  position: absolute;\n  box-sizing: border-box;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  /* @noflip */\n  /*rtl:ignore*/\n  left: 0;\n  border: 1px solid transparent;\n  border-radius: inherit;\n  content: \"\";\n  pointer-events: none;\n}\n@media screen and (forced-colors: active) {\n  .mdc-fab::before {\n    border-color: CanvasText;\n  }\n}\n\n.mdc-fab__label {\n  justify-content: flex-start;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  overflow-x: hidden;\n  overflow-y: visible;\n}\n\n.mdc-fab__icon {\n  transition: transform 180ms 90ms cubic-bezier(0, 0, 0.2, 1);\n  fill: currentColor;\n  will-change: transform;\n}\n\n.mdc-fab .mdc-fab__icon {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.mdc-fab--exited {\n  transform: scale(0);\n  opacity: 0;\n  transition: opacity 15ms linear 150ms, transform 180ms 0ms cubic-bezier(0.4, 0, 1, 1);\n}\n.mdc-fab--exited .mdc-fab__icon {\n  transform: scale(0);\n  transition: transform 135ms 0ms cubic-bezier(0.4, 0, 1, 1);\n}\n\n.mdc-fab {\n  background-color: #018786;\n  /* @alternate */\n  background-color: var(--mdc-theme-secondary, #018786);\n  box-shadow: 0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);\n}\n.mdc-fab .mdc-fab__icon {\n  width: 24px;\n  height: 24px;\n  font-size: 24px;\n}\n.mdc-fab, .mdc-fab:not(:disabled) .mdc-fab__icon, .mdc-fab:not(:disabled) .mdc-fab__label, .mdc-fab:disabled .mdc-fab__icon, .mdc-fab:disabled .mdc-fab__label {\n  color: #fff;\n  /* @alternate */\n  color: var(--mdc-theme-on-secondary, #fff);\n}\n.mdc-fab:not(.mdc-fab--extended) {\n  border-radius: 50%;\n}\n.mdc-fab:not(.mdc-fab--extended) .mdc-fab__ripple {\n  border-radius: 50%;\n}\n\n@keyframes mdc-ripple-fg-radius-in {\n  from {\n    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    /* @noflip */\n    /*rtl:ignore*/\n    transform: translate(var(--mdc-ripple-fg-translate-start, 0)) scale(1);\n  }\n  to {\n    /* @noflip */\n    /*rtl:ignore*/\n    transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));\n  }\n}\n@keyframes mdc-ripple-fg-opacity-in {\n  from {\n    animation-timing-function: linear;\n    opacity: 0;\n  }\n  to {\n    opacity: var(--mdc-ripple-fg-opacity, 0);\n  }\n}\n@keyframes mdc-ripple-fg-opacity-out {\n  from {\n    animation-timing-function: linear;\n    opacity: var(--mdc-ripple-fg-opacity, 0);\n  }\n  to {\n    opacity: 0;\n  }\n}\n.mdc-fab {\n  --mdc-ripple-fg-size: 0;\n  --mdc-ripple-left: 0;\n  --mdc-ripple-top: 0;\n  --mdc-ripple-fg-scale: 1;\n  --mdc-ripple-fg-translate-end: 0;\n  --mdc-ripple-fg-translate-start: 0;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n  will-change: transform, opacity;\n}\n.mdc-fab .mdc-fab__ripple::before,\n.mdc-fab .mdc-fab__ripple::after {\n  position: absolute;\n  border-radius: 50%;\n  opacity: 0;\n  pointer-events: none;\n  content: \"\";\n}\n.mdc-fab .mdc-fab__ripple::before {\n  transition: opacity 15ms linear, background-color 15ms linear;\n  z-index: 1;\n  /* @alternate */\n  z-index: var(--mdc-ripple-z-index, 1);\n}\n.mdc-fab .mdc-fab__ripple::after {\n  z-index: 0;\n  /* @alternate */\n  z-index: var(--mdc-ripple-z-index, 0);\n}\n.mdc-fab.mdc-ripple-upgraded .mdc-fab__ripple::before {\n  transform: scale(var(--mdc-ripple-fg-scale, 1));\n}\n.mdc-fab.mdc-ripple-upgraded .mdc-fab__ripple::after {\n  top: 0;\n  /* @noflip */\n  /*rtl:ignore*/\n  left: 0;\n  transform: scale(0);\n  transform-origin: center center;\n}\n.mdc-fab.mdc-ripple-upgraded--unbounded .mdc-fab__ripple::after {\n  top: var(--mdc-ripple-top, 0);\n  /* @noflip */\n  /*rtl:ignore*/\n  left: var(--mdc-ripple-left, 0);\n}\n.mdc-fab.mdc-ripple-upgraded--foreground-activation .mdc-fab__ripple::after {\n  animation: mdc-ripple-fg-radius-in 225ms forwards, mdc-ripple-fg-opacity-in 75ms forwards;\n}\n.mdc-fab.mdc-ripple-upgraded--foreground-deactivation .mdc-fab__ripple::after {\n  animation: mdc-ripple-fg-opacity-out 150ms;\n  /* @noflip */\n  /*rtl:ignore*/\n  transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));\n}\n.mdc-fab .mdc-fab__ripple::before,\n.mdc-fab .mdc-fab__ripple::after {\n  top: calc(50% - 100%);\n  /* @noflip */\n  /*rtl:ignore*/\n  left: calc(50% - 100%);\n  width: 200%;\n  height: 200%;\n}\n.mdc-fab.mdc-ripple-upgraded .mdc-fab__ripple::after {\n  width: var(--mdc-ripple-fg-size, 100%);\n  height: var(--mdc-ripple-fg-size, 100%);\n}\n.mdc-fab .mdc-fab__ripple::before, .mdc-fab .mdc-fab__ripple::after {\n  background-color: #fff;\n  /* @alternate */\n  background-color: var(--mdc-ripple-color, var(--mdc-theme-on-secondary, #fff));\n}\n.mdc-fab:hover .mdc-fab__ripple::before, .mdc-fab.mdc-ripple-surface--hover .mdc-fab__ripple::before {\n  opacity: 0.08;\n  /* @alternate */\n  opacity: var(--mdc-ripple-hover-opacity, 0.08);\n}\n.mdc-fab.mdc-ripple-upgraded--background-focused .mdc-fab__ripple::before, .mdc-fab:not(.mdc-ripple-upgraded):focus .mdc-fab__ripple::before {\n  transition-duration: 75ms;\n  opacity: 0.24;\n  /* @alternate */\n  opacity: var(--mdc-ripple-focus-opacity, 0.24);\n}\n.mdc-fab:not(.mdc-ripple-upgraded) .mdc-fab__ripple::after {\n  transition: opacity 150ms linear;\n}\n.mdc-fab:not(.mdc-ripple-upgraded):active .mdc-fab__ripple::after {\n  transition-duration: 75ms;\n  opacity: 0.24;\n  /* @alternate */\n  opacity: var(--mdc-ripple-press-opacity, 0.24);\n}\n.mdc-fab.mdc-ripple-upgraded {\n  --mdc-ripple-fg-opacity: var(--mdc-ripple-press-opacity, 0.24);\n}\n.mdc-fab .mdc-fab__ripple {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n  overflow: hidden;\n}\n.mdc-fab {\n  z-index: 0;\n}\n.mdc-fab .mdc-fab__ripple::before,\n.mdc-fab .mdc-fab__ripple::after {\n  z-index: -1;\n  /* @alternate */\n  z-index: var(--mdc-ripple-z-index, -1);\n}","//\n// Copyright 2017 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use '@material/theme/custom-properties';\n@use '@material/base/mixins' as base-mixins;\n@use '@material/feature-targeting/feature-targeting';\n@use '@material/theme/theme';\n@use '@material/theme/theme-color';\n@use './elevation-theme';\n\n@mixin core-styles($query: feature-targeting.all()) {\n  $feat-animation: feature-targeting.create-target($query, animation);\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @for $z-value from 0 through 24 {\n    .mdc-elevation--z#{$z-value} {\n      @include elevation-theme.elevation($z-value, $query: $query);\n    }\n  }\n\n  .mdc-elevation-transition {\n    @include feature-targeting.targets($feat-animation) {\n      transition: elevation-theme.transition-value();\n    }\n\n    @include feature-targeting.targets($feat-structure) {\n      will-change: elevation-theme.$property;\n    }\n  }\n}\n\n///\n/// Called once per application to set up the global default elevation styles.\n///\n@mixin overlay-common($query: feature-targeting.all()) {\n  $feat-animation: feature-targeting.create-target($query, animation);\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  .mdc-elevation-overlay {\n    @include feature-targeting.targets($feat-structure) {\n      @include base-mixins.emit-once('mdc-elevation/common/structure') {\n        position: absolute;\n        border-radius: inherit;\n        pointer-events: none;\n\n        @include theme.property(\n          opacity,\n          custom-properties.create(--mdc-elevation-overlay-opacity, 0)\n        );\n      }\n    }\n\n    @include feature-targeting.targets($feat-animation) {\n      @include base-mixins.emit-once('mdc-elevation/common/animation') {\n        transition: elevation-theme.overlay-transition-value();\n      }\n    }\n\n    @include base-mixins.emit-once('mdc-elevation/common/color') {\n      $fill-color: custom-properties.create(\n        --mdc-elevation-overlay-color,\n        elevation-theme.$overlay-color\n      );\n      @include elevation-theme.overlay-fill-color($fill-color, $query: $query);\n    }\n  }\n}\n","//\n// Copyright 2020 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n@use 'sass:list';\n@use 'sass:map';\n@use 'sass:meta';\n@use './gss';\n\n/// When true, add an additional property/value declaration before declarations\n/// that use advanced features such as custom properties or CSS functions. This\n/// adds fallback support for older browsers such as IE11 that do not support\n/// these features at the cost of additional CSS. Set this variable to false to\n/// disable generating fallback declarations.\n$enable-fallback-declarations: true !default;\n\n/// Writes a CSS property/value declaration. This mixin is used throughout the\n/// theme package for consistency for dynamically setting CSS property values.\n///\n/// This mixin may optionally take a fallback value. For advanced features such\n/// as custom properties or CSS functions like min and max, a fallback value is\n/// recommended to support older browsers.\n///\n/// @param {String} $property - The CSS property of the declaration.\n/// @param {*} $value - The value of the CSS declaration. The value should be\n///     resolved by other theme functions first (i.e. custom property Maps and\n///     Material theme keys are not supported in this mixin). If the value is\n///     null, no declarations will be emitted.\n/// @param {*} $fallback - An optional fallback value for older browsers. If\n///     provided, a second property/value declaration will be added before the\n///     main property/value declaration.\n/// @param {Map} $gss - An optional Map of GSS annotations to add.\n/// @param {Bool} $important - If true, add `!important` to the declaration.\n@mixin declaration(\n  $property,\n  $value,\n  $fallback-value: null,\n  $gss: (),\n  $important: false\n) {\n  // Normally setting a null value to a property will not emit CSS, so mixins\n  // wouldn't need to check this. However, Sass will throw an error if the\n  // interpolated property is a custom property.\n  @if $value != null {\n    $important-rule: if($important, ' !important', '');\n\n    @if $fallback-value and $enable-fallback-declarations {\n      @include gss.annotate($gss);\n      #{$property}: #{$fallback-value} #{$important-rule};\n\n      // Add @alternate to annotations.\n      $gss: map.merge(\n        $gss,\n        (\n          alternate: true,\n        )\n      );\n    }\n\n    @include gss.annotate($gss);\n    #{$property}: #{$value}#{$important-rule};\n  }\n}\n\n/// Unpacks shorthand values for CSS properties (i.e. lists of 1-3 values).\n/// If a list of 4 values is given, it is returned as-is.\n///\n/// Examples:\n///\n/// unpack-value(4px) => 4px 4px 4px 4px\n/// unpack-value(4px 2px) => 4px 2px 4px 2px\n/// unpack-value(4px 2px 2px) => 4px 2px 2px 2px\n/// unpack-value(4px 2px 0 2px) => 4px 2px 0 2px\n///\n/// @param {Number | Map | List} $value - List of 1 to 4 value numbers.\n/// @return {List} a List of 4 value numbers.\n@function unpack-value($value) {\n  @if meta.type-of($value) == 'map' or list.length($value) == 1 {\n    @return $value $value $value $value;\n  } @else if list.length($value) == 4 {\n    @return $value;\n  } @else if list.length($value) == 3 {\n    @return list.nth($value, 1) list.nth($value, 2) list.nth($value, 3)\n      list.nth($value, 2);\n  } @else if list.length($value) == 2 {\n    @return list.nth($value, 1) list.nth($value, 2) list.nth($value, 1)\n      list.nth($value, 2);\n  }\n\n  @error \"Invalid CSS property value: '#{$value}' is more than 4 values\";\n}\n","//\n// Copyright 2020 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n@use 'sass:list';\n@use 'sass:map';\n@use 'sass:meta';\n\n/// Adds optional GSS annotation comments. Useful for theme mixins where one or\n/// more properties are set indirectly.\n///\n/// Annotations may be provided as a Map of annotations or as named arguments.\n///\n/// @example - scss\n///   @include annotate((noflip: true));\n///   left: 0;\n///\n/// @example - scss\n///   @include annotate($noflip: true);\n///   left: 0;\n///\n/// @example - css\n///   /* @noflip */ /*rtl:ignore*/\n///   left: 0;\n///\n/// @param {Map} $annotations - Map of annotations. Values must be set to `true`\n///     for an annotation to be added.\n@mixin annotate($annotations...) {\n  $keywords: meta.keywords($annotations);\n  @if list.length($annotations) > 0 {\n    $annotations: list.nth($annotations, 1);\n  } @else {\n    $annotations: $keywords;\n  }\n\n  