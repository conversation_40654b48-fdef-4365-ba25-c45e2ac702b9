# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [15.0.0-canary.7f224ddd4.0](https://github.com/material-components/material-components-web/compare/v14.0.0...v15.0.0-canary.7f224ddd4.0) (2023-12-28)


### Bug Fixes

* **touch-target:** touch-target mixin now works as expected also when the arguments are custom property strings. ([0e89aab](https://github.com/material-components/material-components-web/commit/0e89aab6b1651c9a22e6caa3c9a9f4b023ff61c3))


### Features

* **touch-target:** margin mixin now also allows custom property maps as arguments. ([dd99c87](https://github.com/material-components/material-components-web/commit/dd99c87645f91ff535df8d50be84ffcfd643ae47))
