{"version":3,"file":"color.min.js","sources":["../src/byte.js","../src/hex.js","../src/hue.js","../packed.js","../src/names.js","../src/rgb.js","../src/srgb.js","../src/color.js","../src/index.esm.js","../src/index.js"],"sourcesContent":["/**\n * @packageDocumentation\n * @module utils\n */\n\n/**\n * Rounds decimal to nearest integer\n * @param {number} v - the number to round\n */\nexport function round(v) {\n  return v + 0.5 | 0;\n}\n\nexport const lim = (v, l, h) => Math.max(Math.min(v, h), l);\n\n/**\n * convert percent to byte 0..255\n * @param {number} v - 0..100\n */\nexport function p2b(v) {\n  return lim(round(v * 2.55), 0, 255);\n}\n\n/**\n * convert byte to percet 0..100\n * @param {number} v - 0..255\n */\nexport function b2p(v) {\n  return lim(round(v / 2.55), 0, 100);\n}\n\n/**\n * convert normalized to byte 0..255\n * @param {number} v - 0..1\n */\nexport function n2b(v) {\n  return lim(round(v * 255), 0, 255);\n}\n\n/**\n * convert byte to normalized 0..1\n * @param {number} v - 0..255\n */\nexport function b2n(v) {\n  return lim(round(v / 2.55) / 100, 0, 1);\n}\n\n/**\n * convert normalized to percent 0..100\n * @param {number} v - 0..1\n */\nexport function n2p(v) {\n  return lim(round(v * 100), 0, 100);\n}\n","/**\n * @packageDocumentation\n * @module utils\n */\n\n/**\n * @typedef {import('./index.js').RGBA} RGBA\n */\n\n/**\n * @hidden\n */\nconst map = {0: 0, 1: 1, 2: 2, 3: 3, 4: 4, 5: 5, 6: 6, 7: 7, 8: 8, 9: 9, A: 10, B: 11, C: 12, D: 13, E: 14, F: 15, a: 10, b: 11, c: 12, d: 13, e: 14, f: 15};\n\n/**\n * @hidden\n */\nconst hex = [...'0123456789ABCDEF'];\n\n/**\n * @param {number} b - byte\n * @hidden\n */\nconst h1 = b => hex[b & 0xF];\n\n/**\n * @param {number} b - byte\n * @hidden\n */\nconst h2 = b => hex[(b & 0xF0) >> 4] + hex[b & 0xF];\n\n/**\n * @param {number} b - byte\n * @hidden\n */\nconst eq = b => ((b & 0xF0) >> 4) === (b & 0xF);\n\n/**\n * @param {RGBA} v - the color\n * @hidden\n */\nconst isShort = v => eq(v.r) && eq(v.g) && eq(v.b) && eq(v.a);\n\n/**\n * Parse HEX to color\n * @param {string} str - the string\n */\nexport function hexParse(str) {\n  var len = str.length;\n  var ret;\n  if (str[0] === '#') {\n    if (len === 4 || len === 5) {\n      ret = {\n        r: 255 & map[str[1]] * 17,\n        g: 255 & map[str[2]] * 17,\n        b: 255 & map[str[3]] * 17,\n        a: len === 5 ? map[str[4]] * 17 : 255\n      };\n    } else if (len === 7 || len === 9) {\n      ret = {\n        r: map[str[1]] << 4 | map[str[2]],\n        g: map[str[3]] << 4 | map[str[4]],\n        b: map[str[5]] << 4 | map[str[6]],\n        a: len === 9 ? (map[str[7]] << 4 | map[str[8]]) : 255\n      };\n    }\n  }\n  return ret;\n}\n\nconst alpha = (a, f) => a < 255 ? f(a) : '';\n\n/**\n * Return HEX string from color\n * @param {RGBA} v - the color\n * @return {string|undefined}\n */\nexport function hexString(v) {\n  var f = isShort(v) ? h1 : h2;\n  return v\n    ? '#' + f(v.r) + f(v.g) + f(v.b) + alpha(v.a, f)\n    : undefined;\n}\n","/**\n * @packageDocumentation\n * @module utils\n */\n\nimport {b2n, n2p, n2b, p2b} from './byte.js';\n\n/**\n * @typedef {import('./index.js').RGBA} RGBA\n */\n\n/**\n * @hidden\n */\nconst HUE_RE = /^(hsla?|hwb|hsv)\\(\\s*([-+.e\\d]+)(?:deg)?[\\s,]+([-+.e\\d]+)%[\\s,]+([-+.e\\d]+)%(?:[\\s,]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\n\n/**\n * Converts hsl to rgb normalized\n * @url https://jsfiddle.net/Lamik/reuk63ay/91\n * @param {number} h - hue [0..360]\n * @param {number} s - saturation [0..1]\n * @param {number} l - lightness [0..1]\n * @returns {number[]} - [r, g, b] each normalized to [0..1]\n * @hidden\n */\nfunction hsl2rgbn(h, s, l) {\n  const a = s * Math.min(l, 1 - l);\n  /**\n   * @param {number} n\n   */\n  const f = (n, k = (n + h / 30) % 12) => l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n  return [f(0), f(8), f(4)];\n}\n\n/**\n * Convert hsv to rgb normalized\n * @url https://jsfiddle.net/Lamik/Lr61wqub/15/\n * @param {number} h - hue [0..360]\n * @param {number} s - saturation [0..1]\n * @param {number} v - value [0..1]\n * @returns {number[]} - [r, g, b] each normalized to [0..1]\n * @hidden\n */\nfunction hsv2rgbn(h, s, v) {\n  /**\n   * @param {number} n\n   */\n  const f = (n, k = (n + h / 60) % 6) => v - v * s * Math.max(Math.min(k, 4 - k, 1), 0);\n  return [f(5), f(3), f(1)];\n}\n\n/**\n * Convert hwb to rgb normalized\n * @param {number} h - hue [0..360]\n * @param {number} w - whiteness [0..1]\n * @param {number} b - blackness [0..1]\n * @returns {number[]} - [r, g, b] each normalized to [0..1]\n * @hidden\n */\nfunction hwb2rgbn(h, w, b) {\n  const rgb = hsl2rgbn(h, 1, 0.5);\n  let i;\n  if (w + b > 1) {\n    i = 1 / (w + b);\n    w *= i;\n    b *= i;\n  }\n  for (i = 0; i < 3; i++) {\n    rgb[i] *= 1 - w - b;\n    rgb[i] += w;\n  }\n  return rgb;\n}\n\nfunction hueValue(r, g, b, d, max) {\n  if (r === max) {\n    return ((g - b) / d) + (g < b ? 6 : 0);\n  }\n  if (g === max) {\n    return (b - r) / d + 2;\n  }\n  return (r - g) / d + 4;\n}\n\n/**\n * Convert rgb to hsl\n * @param {RGBA} v - the color\n * @returns {number[]} - [h, s, l]\n */\nexport function rgb2hsl(v) {\n  const range = 255;\n  const r = v.r / range;\n  const g = v.g / range;\n  const b = v.b / range;\n  const max = Math.max(r, g, b);\n  const min = Math.min(r, g, b);\n  const l = (max + min) / 2;\n  let h, s, d;\n  if (max !== min) {\n    d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    h = hueValue(r, g, b, d, max);\n    h = h * 60 + 0.5;\n  }\n  return [h | 0, s || 0, l];\n}\n\n/**\n * @param {function} f\n * @param {number|number[]} a\n * @param {number} b\n * @param {number} c\n * @private\n * @hidden\n */\nfunction calln(f, a, b, c) {\n  return (\n    Array.isArray(a)\n      ? f(a[0], a[1], a[2])\n      : f(a, b, c)\n  ).map(n2b);\n}\n\n/**\n * Convert hsl to rgb\n * @param {number|number[]} h - hue | [h, s, l]\n * @param {number} [s] - saturation\n * @param {number} [l] - lightness\n * @returns {number[]}\n */\nexport function hsl2rgb(h, s, l) {\n  return calln(hsl2rgbn, h, s, l);\n}\n\n/**\n * Convert hwb to rgb\n * @param {number|number[]} h - hue | [h, s, l]\n * @param {number} [w] - whiteness\n * @param {number} [b] - blackness\n * @returns {number[]}\n */\nexport function hwb2rgb(h, w, b) {\n  return calln(hwb2rgbn, h, w, b);\n}\n\n/**\n * Convert hsv to rgb\n * @param {number|number[]} h - hue | [h, s, l]\n * @param {number} [s] - saturation\n * @param {number} [v] - value\n * @returns {number[]}\n */\nexport function hsv2rgb(h, s, v) {\n  return calln(hsv2rgbn, h, s, v);\n}\n\n/**\n * @param {number} h - the angle\n * @hidden\n */\nfunction hue(h) {\n  return (h % 360 + 360) % 360;\n}\n\n/**\n * Parse hsl/hsv/hwb color string\n * @param {string} str - hsl/hsv/hwb color string\n * @returns {RGBA} - the parsed color components\n */\nexport function hueParse(str) {\n  const m = HUE_RE.exec(str);\n  let a = 255;\n  let v;\n  if (!m) {\n    return;\n  }\n  // v is undefined\n  if (m[5] !== v) {\n    a = m[6] ? p2b(+m[5]) : n2b(+m[5]);\n  }\n  const h = hue(+m[2]);\n  const p1 = +m[3] / 100;\n  const p2 = +m[4] / 100;\n  if (m[1] === 'hwb') {\n    v = hwb2rgb(h, p1, p2);\n  } else if (m[1] === 'hsv') {\n    v = hsv2rgb(h, p1, p2);\n  } else {\n    v = hsl2rgb(h, p1, p2);\n  }\n  return {\n    r: v[0],\n    g: v[1],\n    b: v[2],\n    a: a\n  };\n}\n\n/**\n * Rotate the `v` color by `deg` degrees\n * @param {RGBA} v - the color\n * @param {number} deg - degrees to rotate\n */\nexport function rotate(v, deg) {\n  var h = rgb2hsl(v);\n  h[0] = hue(h[0] + deg);\n  h = hsl2rgb(h);\n  v.r = h[0];\n  v.g = h[1];\n  v.b = h[2];\n}\n\n/**\n * Return hsl(a) string from color components\n * @param {RGBA} v - the color\n * @return {string|undefined}\n */\nexport function hslString(v) {\n  if (!v) {\n    return;\n  }\n  const a = rgb2hsl(v);\n  const h = a[0];\n  const s = n2p(a[1]);\n  const l = n2p(a[2]);\n  return v.a < 255\n    ? `hsla(${h}, ${s}%, ${l}%, ${b2n(v.a)})`\n    : `hsl(${h}, ${s}%, ${l}%)`;\n}\n","\nconst map = {\n\tx: 'dark',\n\tZ: 'light',\n\tY: 're',\n\tX: 'blu',\n\tW: 'gr',\n\tV: 'medium',\n\tU: 'slate',\n\tA: 'ee',\n\tT: 'ol',\n\tS: 'or',\n\tB: 'ra',\n\tC: 'lateg',\n\tD: 'ights',\n\tR: 'in',\n\tQ: 'turquois',\n\tE: 'hi',\n\tP: 'ro',\n\tO: 'al',\n\tN: 'le',\n\tM: 'de',\n\tL: 'yello',\n\tF: 'en',\n\tK: 'ch',\n\tG: 'arks',\n\tH: 'ea',\n\tI: 'ightg',\n\tJ: 'wh'\n};\nconst names = {\n\tOiceXe: 'f0f8ff',\n\tantiquewEte: 'faebd7',\n\taqua: 'ffff',\n\taquamarRe: '7fffd4',\n\tazuY: 'f0ffff',\n\tbeige: 'f5f5dc',\n\tbisque: 'ffe4c4',\n\tblack: '0',\n\tblanKedOmond: 'ffebcd',\n\tXe: 'ff',\n\tXeviTet: '8a2be2',\n\tbPwn: 'a52a2a',\n\tburlywood: 'deb887',\n\tcaMtXe: '5f9ea0',\n\tKartYuse: '7fff00',\n\tKocTate: 'd2691e',\n\tcSO: 'ff7f50',\n\tcSnflowerXe: '6495ed',\n\tcSnsilk: 'fff8dc',\n\tcrimson: 'dc143c',\n\tcyan: 'ffff',\n\txXe: '8b',\n\txcyan: '8b8b',\n\txgTMnPd: 'b8860b',\n\txWay: 'a9a9a9',\n\txgYF: '6400',\n\txgYy: 'a9a9a9',\n\txkhaki: 'bdb76b',\n\txmagFta: '8b008b',\n\txTivegYF: '556b2f',\n\txSange: 'ff8c00',\n\txScEd: '9932cc',\n\txYd: '8b0000',\n\txsOmon: 'e9967a',\n\txsHgYF: '8fbc8f',\n\txUXe: '483d8b',\n\txUWay: '2f4f4f',\n\txUgYy: '2f4f4f',\n\txQe: 'ced1',\n\txviTet: '9400d3',\n\tdAppRk: 'ff1493',\n\tdApskyXe: 'bfff',\n\tdimWay: '696969',\n\tdimgYy: '696969',\n\tdodgerXe: '1e90ff',\n\tfiYbrick: 'b22222',\n\tflSOwEte: 'fffaf0',\n\tfoYstWAn: '228b22',\n\tfuKsia: 'ff00ff',\n\tgaRsbSo: 'dcdcdc',\n\tghostwEte: 'f8f8ff',\n\tgTd: 'ffd700',\n\tgTMnPd: 'daa520',\n\tWay: '808080',\n\tgYF: '8000',\n\tgYFLw: 'adff2f',\n\tgYy: '808080',\n\thoneyMw: 'f0fff0',\n\thotpRk: 'ff69b4',\n\tRdianYd: 'cd5c5c',\n\tRdigo: '4b0082',\n\tivSy: 'fffff0',\n\tkhaki: 'f0e68c',\n\tlavFMr: 'e6e6fa',\n\tlavFMrXsh: 'fff0f5',\n\tlawngYF: '7cfc00',\n\tNmoncEffon: 'fffacd',\n\tZXe: 'add8e6',\n\tZcSO: 'f08080',\n\tZcyan: 'e0ffff',\n\tZgTMnPdLw: 'fafad2',\n\tZWay: 'd3d3d3',\n\tZgYF: '90ee90',\n\tZgYy: 'd3d3d3',\n\tZpRk: 'ffb6c1',\n\tZsOmon: 'ffa07a',\n\tZsHgYF: '20b2aa',\n\tZskyXe: '87cefa',\n\tZUWay: '778899',\n\tZUgYy: '778899',\n\tZstAlXe: 'b0c4de',\n\tZLw: 'ffffe0',\n\tlime: 'ff00',\n\tlimegYF: '32cd32',\n\tlRF: 'faf0e6',\n\tmagFta: 'ff00ff',\n\tmaPon: '800000',\n\tVaquamarRe: '66cdaa',\n\tVXe: 'cd',\n\tV